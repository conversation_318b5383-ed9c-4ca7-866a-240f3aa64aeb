import React, { useState, useEffect, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import InvoiceItem from "./InvoiceItem";
import { BsArrowRepeat } from "react-icons/bs";
import { LiaSpinnerSolid } from "react-icons/lia";
import Categories from "./Categories";
import LoadCustomerModal from "./table/LoadCutomerModal";
import PaymentModal from './posModals/reportModals/paynowmodal';
import HoldOrdersModal from './posModals/reportModals/HoldOrdersModal';

import GiftCardModal from './posModals/reportModals/GiftCardModal';
import DiscountModal from './posModals/reportModals/DiscountModal';
import ManagerDiscountModal from './posModals/reportModals/ManagerDiscountModal';
import ItemDiscountModal from './posModals/reportModals/ItemDiscountModal';
import ReasonModal from './posModals/ReasonModal';
import {

  selectCartItems,
  selectCartTotal,
  setDiscountAmount as setCartDiscountAmount,
  setDiscountReason as setCartDiscountReason,
  setDiscountType as setCartDiscountType,
  setTaxPercentage as setCartTaxPercentage,
  setLoyaltyPercentage as setCartLoyaltyPercentage,
  setLoyaltyFixedAmount as setCartLoyaltyFixedAmount,
  setLoyaltyType as setCartLoyaltyType,
  selectFinalTotal,
  selectInvoiceNumber,
  selectTipAmount,
  selectLoyaltyAmount,
  selectLoyaltyPercentage,
  generateNewInvoiceNumber,
  setLoyaltyType,
  selectLoyaltyType,
  selectDiscountAmount,
  selectDiscountReason,
  selectDiscountType,
  selectAllForRefund,
  toggleSelectAllForRefund,
  selectIsRefundMode,
  exitRefundMode,
  selectTaxPercentage,

  setSelectedCoupon,
  setCouponOfferAmount,
  clearCoupon,
  clearCart
} from "../store/slices/cartSlice";
import { useGetTaxesQuery } from "../store/api/pos/orderapi";
import { useNavigate } from "react-router-dom";
import { clearSelectedCustomer, selectSelectedCustomer } from "../store/slices/selectedcustomer";
import { addHeldOrder, selectHeldOrders } from "../store/slices/holdorder";
import type { RootState } from "../store/store";
import { selectSelectedTables } from "../store/slices/selectedTablesSlice";
import Swal from "sweetalert2";

interface InvoicePanelProps {
  isInModal?: boolean;
}

const InvoicePanel: React.FC<InvoicePanelProps> = ({ isInModal = false }) => {
  const dispatch = useDispatch();
  const items = useSelector(selectCartItems);
  const cartTotal = useSelector(selectCartTotal);
  const navigate = useNavigate()

  // Debug log for modal
  useEffect(() => {
    if (isInModal) {
      console.log("InvoicePanel in modal - items:", items);
      console.log("InvoicePanel in modal - items length:", items.length);
    }
  }, [isInModal, items]);
  const finalTotal = useSelector(selectFinalTotal);
  const invoiceNumber = useSelector(selectInvoiceNumber);
  const tipAmount = useSelector(selectTipAmount);
  const loyaltyAmount = useSelector(selectLoyaltyAmount);
  const loyaltyPercentage = useSelector(selectLoyaltyPercentage);
  const loyaltyType = useSelector(selectLoyaltyType)
  const heldOrders = useSelector(selectHeldOrders);
  const selectedtable = useSelector(selectSelectedTables)

  const [isModalOpen, setIsModalOpen] = useState(false);
  const cart = useSelector((state: RootState) => state.cart)
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false);
  const [isHoldOrdersModalOpen, setIsHoldOrdersModalOpen] = useState(false);

  const [isGiftCardModalOpen, setIsGiftCardModalOpen] = useState(false);
  const [isDiscountDropdownOpen, setIsDiscountDropdownOpen] = useState(false);
  const [isDiscountModalOpen, setIsDiscountModalOpen] = useState(false);
  const [isManagerDiscountModalOpen, setIsManagerDiscountModalOpen] = useState(false);
  const [isReceiptDiscountModalOpen, setIsReceiptDiscountModalOpen] = useState(false);
  const [isItemDiscountModalOpen, setIsItemDiscountModalOpen] = useState(false);
  const [isReasonModalOpen, setIsReasonModalOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string | undefined>(undefined);
  const [_selectedDiscount, setSelectedDiscount] = useState<string | null>(null);

  // Local state for UI display - removed as we're using Redux state
  useEffect(() => {
    dispatch(generateNewInvoiceNumber())
  }, [])

  const userId = localStorage.getItem("userId") || ""
  const { data: tax } = useGetTaxesQuery(userId)
  console.log("the tax is ", tax)

  // Calculate total tax percentage from API data
  const calculateTotalTaxPercentage = () => {
    if (!tax || !Array.isArray(tax)) return 0;

    return tax.reduce((total, taxItem) => {
      return total + (parseFloat(taxItem.taxValue) || 0);
    }, 0);
  };

  const totalTaxPercentage = calculateTotalTaxPercentage();

  // Get selectors first
  const selectedCustomer = useSelector(selectSelectedCustomer);
  const currentDiscountAmount = useSelector(selectDiscountAmount);
  const currentDiscountReason = useSelector(selectDiscountReason);
  const currentDiscountType = useSelector(selectDiscountType);
  const selectAllRefund = useSelector(selectAllForRefund);
  const isRefundMode = useSelector(selectIsRefundMode);
  const currentTaxPercentage = useSelector(selectTaxPercentage);

  // Update Redux state when tax data changes (only when not in refund mode)
  useEffect(() => {
    if (totalTaxPercentage > 0 && !isRefundMode) {
      dispatch(setCartTaxPercentage(totalTaxPercentage));
    }
  }, [totalTaxPercentage, dispatch, isRefundMode]);

  const [_selectedGiftCard, setSelectedGiftCard] = useState<{
    cardNumber: string;
    customerName: string;
    balance: number;
  } | null>(null);

  const openModal = () => {
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
  };

  const handleAddNewCustomer = () => {
    console.log("Add new customer logic here");
    closeModal();
  };



  const openGiftCardModal = () => {
    setIsGiftCardModalOpen(true);
  };

  const closeGiftCardModal = () => {
    setIsGiftCardModalOpen(false);
  };

  const handleGiftCardApply = (cardNumber: string, customerName: string, balance: number) => {
    setSelectedGiftCard({ cardNumber, customerName, balance });
    console.log(`Gift card applied: ${cardNumber}, ${customerName}, $${balance}`);
  };

  const discountDropdownRef = useRef<HTMLDivElement>(null);

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (discountDropdownRef.current && !discountDropdownRef.current.contains(event.target as Node)) {
        setIsDiscountDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const toggleDiscountDropdown = () => {
    setIsDiscountDropdownOpen(prev => !prev);
  };

  const handleDiscountSelect = (discountType: string) => {
    setSelectedDiscount(discountType);
    setIsDiscountDropdownOpen(false);
    console.log(`Discount selected: ${discountType}`);

    if (discountType === "Add Discount") {
      setIsDiscountModalOpen(true);
    } else if (discountType === "Manager Discount") {
      setIsManagerDiscountModalOpen(true);
    } else if (discountType === "Receipt Discount") {
      setIsReceiptDiscountModalOpen(true);
    } else if (discountType === "Item Discount") {
      setIsItemDiscountModalOpen(true);
    }
  };

  // const openDiscountModal = () => {
  //   setIsDiscountModalOpen(true);
  // };

  const closeDiscountModal = () => {
    setIsDiscountModalOpen(false);
  };

  // const openManagerDiscountModal = () => {
  //   setIsManagerDiscountModalOpen(true);
  // };

  const closeManagerDiscountModal = () => {
    setIsManagerDiscountModalOpen(false);
  };

  // const openReceiptDiscountModal = () => {
  //   setIsReceiptDiscountModalOpen(true);
  // };

  const closeReceiptDiscountModal = () => {
    setIsReceiptDiscountModalOpen(false);
  };

  const closeItemDiscountModal = () => {
    setIsItemDiscountModalOpen(false);
  };

  const closeReasonModal = () => {
    setIsReasonModalOpen(false);
  };

  const handleSaveReason = (reason: string) => {
    console.log(`Refund reason saved: ${reason}`);
    // Here you can add logic to save the reason to Redux or send to API
  };

  const handleApplyDiscount = (amount: string, selectedDiscount: any = null, loyaltyData?: { amount: string, percentage?: number, type: string }) => {
    // Only set discount amount, don't touch loyalty
    dispatch(setCartDiscountAmount(amount));

    // Store coupon information if a coupon was selected
    if (selectedDiscount) {
      dispatch(setSelectedCoupon({
        _id: selectedDiscount.id,
        series: selectedDiscount.series,
        description: selectedDiscount.description,
        discount: selectedDiscount.discount,
        discountType: selectedDiscount.discountType
      }));

      // Calculate coupon offer amount based on discount type
      let couponOfferAmount = 0;
      if (selectedDiscount.discountType === '%') {
        // For percentage discounts, calculate based on cart total
        const cartTotal = items.reduce((total, item) => total + (item.price * item.quantity), 0);
        couponOfferAmount = (cartTotal * selectedDiscount.discount) / 100;
      } else {
        // For fixed amount discounts
        couponOfferAmount = selectedDiscount.discount;
      }
      dispatch(setCouponOfferAmount(couponOfferAmount));
    } else {
      // Clear coupon if no discount selected
      dispatch(clearCoupon());
    }

    // Set loyalty data if provided, otherwise reset loyalty to zero
    if (loyaltyData) {
      dispatch(setCartLoyaltyType(loyaltyData.type));

      if (loyaltyData.type === 'percentage' && loyaltyData.percentage !== undefined) {
        dispatch(setCartLoyaltyPercentage(loyaltyData.percentage));
        dispatch(setLoyaltyType('percentage'))
      } else if (loyaltyData.type === 'fixed') {
        dispatch(setCartLoyaltyFixedAmount(loyaltyData.amount));
        dispatch(setLoyaltyType('fixed'))
      }
    } else {
      // Reset loyalty to zero when no loyalty data is provided
      dispatch(setCartLoyaltyType(''));
      dispatch(setCartLoyaltyPercentage(0));
      dispatch(setCartLoyaltyFixedAmount('0'));
    }

    console.log(`Discount amount applied: ${amount}`);
    if (selectedDiscount) {
      console.log(`Coupon applied:`, selectedDiscount);
    }
    if (loyaltyData) {
      console.log(`Loyalty applied:`, loyaltyData);
    }
  };

  const handleApplyManagerDiscount = (amount: string, reason: string, discountType: string) => {
    // Use the renamed action creators from cartSlice
    dispatch(setCartDiscountAmount(amount));
    dispatch(setCartDiscountReason(reason));
    dispatch(setCartDiscountType(discountType));
    console.log(`Manager discount applied: ${amount}, Reason: ${reason}, Type: ${discountType}`);
  };

  const handleApplyReceiptDiscount = (amount: string, reason: string, discountType: string) => {
    // Use the renamed action creators from cartSlice
    dispatch(setCartDiscountAmount(amount));
    dispatch(setCartDiscountReason(reason));
    dispatch(setCartDiscountType(discountType));
    console.log(`Receipt discount applied: ${amount}, Reason: ${reason}, Type: ${discountType}`);
  };




  function handleResetCustomer(_event: React.MouseEvent<SVGElement, MouseEvent>): void {
    throw new Error("Function not implemented.");
  }

  function handlehold() {
    console.log("the hold order is" + heldOrders)
    if (items.length > 0) {
      // Get table information from selected tables
      const tableNo = selectedtable.length > 0
        ? selectedtable.map(table => table.number || table.name).join(', ')
        : 'No Table';

      // Get customer information
      const customerName = selectedCustomer
        ? `${selectedCustomer.FirstName} ${selectedCustomer.LastName}`
        : 'Guest';

      const customerId = selectedCustomer?._id || undefined;

      dispatch(addHeldOrder({
        cartState: {
          ...cart
        },
        orderInfo: {
          tableNo: tableNo,
          operatorName: "Admin", // You might want to get this from user context
          customer: customerName,
          customerId: customerId,
          amount: finalTotal
        }
      }));

      // Clear the cart after holding the order
      dispatch(clearCart());

      // Show success alert
      Swal.fire({
        icon: 'success',
        title: 'Order Held Successfully!',
        html: `
          <div style="text-align: left;">
            <p><strong>Table:</strong> ${tableNo}</p>
            <p><strong>Customer:</strong> ${customerName}</p>
            <p><strong>Amount:</strong> $${finalTotal.toFixed(2)}</p>
          </div>
        `,
        confirmButtonText: 'OK',
        confirmButtonColor: '#f97316',
        timer: 3000,
        timerProgressBar: true,
        showClass: {
          popup: 'animate__animated animate__fadeInDown'
        },
        hideClass: {
          popup: 'animate__animated animate__fadeOutUp'
        }
      });

      console.log(`Order held for table(s): ${tableNo}`);
      return;
    }
    setIsHoldOrdersModalOpen(true)
  }


  return (
    <div className={`bg-white pt-3 rounded-xl flex flex-col overflow-hidden ${isInModal ? 'h-full' : 'h-[88vh]'}`}>
      <div className="px-4">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-bold mb-1">Invoice</h2>
          <p className="font-bold">{invoiceNumber}</p>
        </div>

        <div className="flex items-center justify-between mb-2 border-b border-[#E4E4E4] pb-2">
          <h2 className="font-medium text-gray-600">Customer</h2>
          <div className="flex items-center gap-2">
            <h3 className="text-lg font-semibold">
              {selectedCustomer
                ? `${selectedCustomer?.FirstName} ${selectedCustomer?.LastName}`
                : "Guest"}
            </h3>
            <LiaSpinnerSolid
              className="text-gray-500 cursor-pointer hover:text-gray-700"
              size={28}
              onClick={openModal}
              title="Load Customer"
            />
            <BsArrowRepeat
              className="text-orange-500 cursor-pointer hover:text-orange-600"
              size={20}
              onClick={() => {
                dispatch(clearSelectedCustomer());
                if (isRefundMode) {
                  dispatch(exitRefundMode());
                }
              }}
              title={isRefundMode ? "Exit Refund Mode" : "Reset Cart"}
            />

          </div>
        </div>

        <div className="mb-2">
          <Categories
            categories={["Discount", "Gift Card", "Table"]}
            selected={selectedCategory}
            onSelect={(category) => {
              setSelectedCategory(category);
              if (category === "Gift Card") {
                console.log("Gift card selected");
                openGiftCardModal();
              } else if (category === "Discount") {
                console.log("Discount selected");
                toggleDiscountDropdown();
              } else if (category === "Table") {
                navigate("/tables")
              }
            }}
          />

          {/* Discount Dropdown */}
          {isDiscountDropdownOpen && (
            <div
              ref={discountDropdownRef}
              className="absolute left-5 top-36 w-48 bg-white border border-gray-200 rounded-md shadow-lg z-50"
            >
              <div className="py-1">
                <button
                  onClick={() => handleDiscountSelect("Add Discount")}
                  className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-orange-50 hover:text-orange-500 border-b border-gray-100"
                >
                  Add Discount
                </button>
                <button
                  onClick={() => handleDiscountSelect("Item Discount")}
                  className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-orange-50 hover:text-orange-500 border-b border-gray-100"
                >
                  Item Discount
                </button>
                <button
                  onClick={() => handleDiscountSelect("Manager Discount")}
                  className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-orange-50 hover:text-orange-500 border-b border-gray-100"
                >
                  Manager Discount
                </button>
                <button
                  onClick={() => handleDiscountSelect("Receipt Discount")}
                  className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-orange-50 hover:text-orange-500"
                >
                  Receipt Discount
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Select All for Refund - Only show in refund mode */}
      {isRefundMode && (
        <div className="px-4 py-2">
          <label className="flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={selectAllRefund}
              onChange={() => dispatch(toggleSelectAllForRefund())}
              className="mr-2 w-4 h-4 text-orange-500 border-gray-300 rounded focus:ring-orange-500"
            />
            <span className="text-sm font-medium text-gray-700">Select All for Refund</span>
          </label>
        </div>
      )}

      {/* Scrollable Items */}
      <div className="flex-1 overflow-y-auto px-4 space-y-2 min-h-0">
        {items.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-8 text-gray-500">
            <svg className="w-16 h-16 mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
            </svg>
            <p className="text-lg font-medium">No items in cart</p>
            <p className="text-sm">Add products to see them here</p>
          </div>
        ) : (
          items.map((item: any) => (
            <InvoiceItem
              key={item.id}
              id={item.id}
              name={item.name}
              price={item.price}
              image={item.image}
              quantity={item.quantity}
              modifiers={item.modifiers}
              hasModifiers={item.hasModifiers}
              note={item.note}
              discountType={item.discountType}
              discountAmount={item.discountAmount}
              originalPrice={item.originalPrice}
              selectedForRefund={item.selectedForRefund}
              isRefundMode={isRefundMode}
            />
          ))
        )}
      </div>

      {/* Sticky Bottom Panel */}
      <div className="px-4 py-1 border-t border-[#E4E4E4] bg-white mt-auto">
        <div className="flex justify-between text-[16px] mb-2">
          <span className="text-natural">Item Total</span>
          <span className="text-black font-bold">${cartTotal.toFixed(2)}</span>
        </div>
        <div className="flex justify-between text-[16px] mb-2">
          <span className="text-natural">
            {currentDiscountReason ? `Discount (${currentDiscountReason})` : 'Discount'}
            {currentDiscountType === 'percentage' && currentDiscountAmount ? ` ${currentDiscountAmount}%` : ''}
          </span>
          <span className="text-black font-bold">
            ${currentDiscountType === 'percentage'
              ? ((cartTotal * (Number(currentDiscountAmount) || 0)) / 100).toFixed(2)
              : (Number(currentDiscountAmount) || 0).toFixed(2)
            }
          </span>
        </div>
        <div className="flex justify-between text-[16px] mb-2">
          <span className="text-gray-600">
            Loyalty {loyaltyType === 'percentage' && loyaltyPercentage > 0 ? `(${loyaltyPercentage}%)` : ''}
          </span>
          <span className="text-black font-bold">
            ${loyaltyAmount ? loyaltyAmount.toFixed(2) : "0.00"}
          </span>
        </div>
        <div className="flex justify-between text-[16px] mb-2">
          <span className="text-natural">Tip</span>
          <span className="text-black font-bold">
            ${tipAmount ? (Number(tipAmount) || 0).toFixed(2) : "0.00"}
          </span>
        </div>
        <div className="flex justify-between text-[16px] mb-2">
          <span className="text-natural">Tax ({currentTaxPercentage}%)</span>
          <span className="text-black font-bold">
            ${(cartTotal * (currentTaxPercentage / 100)).toFixed(2)}
          </span>
        </div>
        <div className="flex justify-between text-[16px] mb-4 border-t border-gray-200 pt-2">
          <span className="text-natural font-bold">Total</span>
          <span className="text-orange font-bold text-xl">
            ${finalTotal.toFixed(2)}
          </span>
        </div>

        <div className="flex gap-2">
          {isRefundMode ? (
            <>
              <button
                className="flex-1 bg-orange text-white font-bold text-sm py-2 rounded-full cursor-pointer"
                onClick={() => dispatch(clearCart())}
              >
                Refund Item
              </button>
              <button
                onClick={() => setIsReasonModalOpen(true)}
                className="flex-1 bg-orange text-white font-bold text-sm py-2 rounded-full cursor-pointer transition-colors"
              >
                Reason
              </button>
              <button 
                onClick={() => setIsPaymentModalOpen(true)} 
                className={`flex-1 font-bold text-sm py-2 rounded-full ${
                  items.length === 0 
                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed' 
                    : 'bg-orange text-white cursor-pointer hover:bg-orange-600 transition-colors'
                }`}
                disabled={items.length === 0}
              >
                Pay Now
              </button>
            </>
          ) : (
            <>
              <button
                onClick={handlehold}
                className="flex-1 bg-orange text-white font-bold text-sm py-2 rounded-full cursor-pointer"
              >
                Hold
              </button>
              <button className="flex-1 bg-orange text-white font-bold text-sm py-2 rounded-full cursor-pointer transition-colors">
                Split
              </button>
              <button 
                onClick={() => setIsPaymentModalOpen(true)} 
                className={`flex-1 font-bold text-sm py-2 rounded-full ${
                  items.length === 0 
                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed' 
                    : 'bg-orange text-white cursor-pointer hover:bg-orange-600 transition-colors'
                }`}
                disabled={items.length === 0}
              >
                Pay Now
              </button>
            </>
          )}
        </div>
      </div>

      {/* Modals */}
      <LoadCustomerModal
        isOpen={isModalOpen}
        onClose={closeModal}
        onAddNewCustomer={handleAddNewCustomer}
        onSelectCustomer={handleResetCustomer}
      />

      <PaymentModal
        isOpen={isPaymentModalOpen}
        onClose={() => setIsPaymentModalOpen(false)}
        totalAmount={finalTotal}
      />

      <HoldOrdersModal
        isOpen={isHoldOrdersModalOpen}
        onClose={() => setIsHoldOrdersModalOpen(false)}
      />

      <GiftCardModal
        isOpen={isGiftCardModalOpen}
        onClose={closeGiftCardModal}
        onApply={handleGiftCardApply}
      />

      <DiscountModal
        isOpen={isDiscountModalOpen}
        onClose={closeDiscountModal}
        onApply={handleApplyDiscount}
      />

      <ManagerDiscountModal
        isOpen={isManagerDiscountModalOpen}
        onClose={closeManagerDiscountModal}
        onApply={handleApplyManagerDiscount}
        title="Manager Discount"
        initialAmount={currentDiscountAmount}
        initialReason={currentDiscountReason}
        initialDiscountType={currentDiscountType}
      />

      <ManagerDiscountModal
        isOpen={isReceiptDiscountModalOpen}
        onClose={closeReceiptDiscountModal}
        onApply={handleApplyReceiptDiscount}
        title="Receipt Discount"
        initialAmount={currentDiscountAmount}
        initialReason={currentDiscountReason}
        initialDiscountType={currentDiscountType}
      />

      <ItemDiscountModal
        isOpen={isItemDiscountModalOpen}
        onClose={closeItemDiscountModal}
        items={items}
      />

      <ReasonModal
        isOpen={isReasonModalOpen}
        onClose={closeReasonModal}
        onSave={handleSaveReason}
      />
    </div >
  );
};

export default InvoicePanel;