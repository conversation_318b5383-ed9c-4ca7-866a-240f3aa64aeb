import React from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { IoClose } from "react-icons/io5";
import { MdOutlineEdit, MdOutlinePrint, MdPayment } from "react-icons/md";
import {
    selectHeldOrdersSortedByDate,
    removeHeldOrder,
} from '../../store/slices/holdorder';
import {
    setCartItems,
    setDiscountAmount,
    setDiscountReason,
    setDiscountType,
    setTaxPercentage,
    setTipAmount,
    setLoyaltyPercentage,
    setLoyaltyFixedAmount,
    setLoyaltyType,
    setSelectedCoupon,
    setCouponOfferAmount,
} from '../../store/slices/cartSlice';
import { useGetCustomersQuery } from '../../store/api/pos/customer';
import { setSelectedCustomer } from '../../store/slices/selectedcustomer';
import { setSelectedTables, clearSelectedTables } from '../../store/slices/selectedTablesSlice';
import { selectTables } from '../../store/slices/TableManagementSlice';

interface RunningOrdersModalProps {
    isOpen: boolean;
    onClose: () => void;
}

const modalVariants = {
    hidden: { x: '100%' },   // Off-screen right
    visible: { x: 0 },       // In place
    exit: { x: '100%' },     // Slide out to right
};

const RunningOrdersModal: React.FC<RunningOrdersModalProps> = ({ isOpen, onClose }) => {
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const userId = localStorage.getItem("userId") || "";
    const { data: customers } = useGetCustomersQuery(userId);

    // Get held orders from Redux store
    const heldOrders = useSelector(selectHeldOrdersSortedByDate);
    const tables = useSelector(selectTables);

    // Map held orders to the format expected by the component
    const orders = heldOrders.map(order => {
        // Calculate dining time in "Xh Ym ago" format based on record date
        const orderDate = new Date(order.recordDate);
        const now = new Date();
        const diffMs = now.getTime() - orderDate.getTime();
        const diffHrs = Math.floor(diffMs / (1000 * 60 * 60));
        const diffMins = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
        const diningTime = `${diffHrs}h ${diffMins}m ago`;

        // Get first item details or placeholder
        const orderDetails = order.items.length > 0
            ? `${order.items[0].name} x${order.items[0].quantity}${order.items.length > 1 ? '...' : ''}`
            : 'No items';

        return {
            tableNumber: order.tableNo || "No Table",
            orderNumber: order.orderNo,
            orderDetails: orderDetails,
            amount: order.amount,
            status: "Running Order", // Default status for all held orders
            dinningFor: diningTime
        };
    });

    const getStatusColor = (status: string) => {
        switch (status) {
            case "Running Order":
                return "bg-blue-100 text-blue-600";
            case "Done soon":
                return "bg-orange-100 text-orange-600";
            case "Done":
                return "bg-green-100 text-green-600";
            default:
                return "bg-gray-100 text-gray-600";
        }
    };

    const handleEditInfo = (orderNumber: string) => {
        const orderNo = orderNumber.startsWith('#') ? orderNumber.substring(1) : orderNumber;
        const selectedOrder = heldOrders.find(order => order.orderNo === orderNo);

        if (!selectedOrder) return;

        // Load the held order data back into the cart
        dispatch(setCartItems(selectedOrder.items));
        dispatch(setDiscountAmount(selectedOrder.discountAmount));
        dispatch(setDiscountReason(selectedOrder.discountReason));
        dispatch(setDiscountType(selectedOrder.discountType));
        dispatch(setTaxPercentage(selectedOrder.taxPercentage));
        dispatch(setTipAmount(selectedOrder.tipAmount));
        dispatch(setLoyaltyPercentage(selectedOrder.loyaltyPercentage));
        dispatch(setLoyaltyFixedAmount(selectedOrder.loyaltyFixedAmount));
        dispatch(setLoyaltyType(selectedOrder.loyaltyType));
        dispatch(setSelectedCoupon(selectedOrder.selectedCoupon));
        dispatch(setCouponOfferAmount(selectedOrder.couponOfferAmount));

        // Handle table selection
        if (selectedOrder.tableNo && selectedOrder.tableNo !== '-' && selectedOrder.tableNo !== 'No Table') {
            // Split the table numbers if multiple tables are joined with comma
            const tableNumbers = selectedOrder.tableNo.split(',').map(t => t.trim());

            // Find complete information for each table
            const selectedTables = tableNumbers.map(tableNo => {
                const tableInfo = tables.find(table => table.tableNo === tableNo);

                if (tableInfo) {
                    // If table found in TableManagementSlice, use complete information
                    return {
                        id: tableInfo._id || tableNo,
                        name: tableInfo.tableName || tableNo,
                        number: tableInfo.tableNo
                    };
                } else {
                    // If table not found, use the table number as fallback
                    return {
                        id: tableNo,
                        name: tableNo,
                        number: tableNo
                    };
                }
            });

            // Set all selected tables
            dispatch(setSelectedTables(selectedTables));
        } else {
            // If no table in held order, clear selected tables
            dispatch(clearSelectedTables());
        }

        // Handle customer selection if available
        if (selectedOrder.customerId && customers) {
            const customer = customers.find((c) => c?.id === selectedOrder.customerId);
            if (customer) {
                // Map Customer to SelectedCustomer format
                const selectedCustomerData = {
                    _id: customer.id,
                    CustomerId: customer.id,
                    FirstName: customer.firstName,
                    LastName: customer.lastName,
                    Email: customer.email,
                    Phone: customer.phone,
                    Address: customer.address,
                    City: customer.city,
                    State: customer.state,
                    isActive: true,
                    userId: userId,
                    CardNo: customer.cardNo || '',
                    LastVisit: customer.lastVisit || '',
                    creditLimits: 0,
                    timeStamp: new Date().toISOString(),
                    __v: 0,
                    CustomerLoyalty: customer.CustomerLoyalty
                };
                dispatch(setSelectedCustomer(selectedCustomerData));
            }
        }

        // Remove the held order after loading it
        dispatch(removeHeldOrder(orderNo));

        // Navigate to home screen
        navigate('/pos/pos-dashboard');
        onClose();
    };

    const getActionButtons = (status: string, orderNumber: string) => {
        switch (status) {
            case "Running Order":
                return (
                    <div className="flex space-x-3 items-center">
                        <button
                            className="px-6 py-[6px] text-sm font-semibold cursor-pointer border border-orange-500 text-orange-500 rounded-full flex items-center justify-center whitespace-nowrap"
                            onClick={(e) => {
                                e.stopPropagation();
                                handleEditInfo(orderNumber);
                            }}
                        >
                            <MdOutlineEdit className="w-4 h-4 mr-1" />
                            Edit info
                        </button>
                        <button className="px-6 py-[6px] text-sm font-semibold cursor-pointer border border-orange-500 text-orange-500 rounded-full flex items-center justify-center whitespace-nowrap">
                            <MdOutlinePrint className="w-4 h-4 mr-1" />
                            Print Bill
                        </button>
                    </div>
                );
            case "Done soon":
                return (
                    <div className="flex space-x-3 items-center">
                        <button className="px-6 py-[6px] text-sm font-semibold cursor-pointer border border-orange-500 text-orange-500 rounded-full flex items-center justify-center whitespace-nowrap">
                            <MdOutlinePrint className="w-4 h-4 mr-1" />
                            Re-print
                        </button>
                        <button className="px-6 py-[6px] text-sm font-semibold cursor-pointer bg-orange-500 text-white rounded-full flex items-center justify-center whitespace-nowrap">
                            <MdPayment className="w-4 h-4 mr-1" />
                            Pay Now
                        </button>
                    </div>
                );
            default:
                return null;
        }
    };

    return (
        <AnimatePresence>
            {isOpen && (
                <div className="fixed inset-0 z-50 flex justify-end">
                    <div className="bg-black/50 absolute inset-0" onClick={onClose} />
                    <motion.div
                        className="relative xl:w-[81vw] w-full bg-white h-full flex flex-col"
                        variants={modalVariants}
                        initial="hidden"
                        animate="visible"
                        exit="exit"
                        transition={{ type: "tween", duration: 0.35 }}
                    >
                        <div className="flex items-center justify-between p-4">
                            <h2 className="text-xl font-semibold">Running Orders</h2>
                            <button onClick={onClose} className='text-gray-400 hover:text-gray-600 cursor-pointer'>
                                <IoClose size={28} />
                            </button>
                        </div>

                        <div className="flex-1 overflow-y-auto px-7 pt-2">
                            <div className="grid grid-cols-16 px-4 py-2 text-sm text-gray-500 border-b border-[#E4E4E4]">
                                <div className="col-span-2">Table Number</div>
                                <div className="col-span-3">Order Number</div>
                                <div className="col-span-2">Amount</div>
                                <div className="col-span-2">Status</div>
                                <div className="col-span-2">Dinning For</div>
                            </div>

                            {/* Order List */}
                            {orders.length > 0 ? (
                                orders.map((order, index) => (
                                    <div key={index} className="grid grid-cols-16 px-4 py-3 items-center border-b border-[#E4E4E4] hover:bg-gray-50">
                                        <div className="col-span-2">
                                            <span className="px-3 py-4 bg-[#FFF5EE] text-orange-500 rounded-full font-semibold text-sm">
                                                {order.tableNumber}
                                            </span>
                                        </div>
                                        <div className="col-span-3">
                                            <div className="font-semibold">{order.orderNumber}</div>
                                            <div className="text-xs text-gray-500">{order.orderDetails}</div>
                                        </div>
                                        <div className="col-span-2 text-sm">${order.amount.toFixed(2)}</div>
                                        <div className="col-span-2">
                                            <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                                                {order.status}
                                            </span>
                                        </div>
                                        <div className="col-span-2 text-sm">{order.dinningFor}</div>
                                        <div className="col-span-2">
                                            {getActionButtons(order.status, order.orderNumber)}
                                        </div>
                                    </div>
                                ))
                            ) : (
                                <div className="py-8 text-center text-gray-500">
                                    No running orders available
                                </div>
                            )}
                        </div>
                    </motion.div>
                </div>
            )}
        </AnimatePresence>
    );
};

export default RunningOrdersModal;