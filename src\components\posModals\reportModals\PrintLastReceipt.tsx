import React, { useRef } from "react";
import CustomModal from "../../CustomModal";
import { Printer } from "lucide-react";
import { useGetLastOrderQuery } from "../../../store/api/pos/orderapi";

interface PrintLastReceiptProps {
  isOpen: boolean;
  onClose: () => void;
}

interface OrderItem {
  name: string;
  qty: number;
  price: number;
  subtotal: number;
}

const PrintLastReceipt: React.FC<PrintLastReceiptProps> = ({
  isOpen,
  onClose,
}) => {
  const contentRef = useRef<HTMLDivElement>(null);
  const userId = localStorage.getItem("userId") || " ";
  const { data, isLoading, error } = useGetLastOrderQuery(userId);

  // Function to handle printing
  const handlePrint = () => {
    const printWindow = window.open('', '_blank');
    if (!printWindow) return;

    const printContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Order Receipt</title>
          <style>
            * {
              margin: 0;
              padding: 0;
              box-sizing: border-box;
            }
            body {
              font-family: Arial, sans-serif;
              font-size: 12px;
              line-height: 1.4;
              color: #000;
              background: white;
              padding: 20px;
              max-width: 600px;
              margin: 0 auto;
            }
            .receipt-header {
              text-align: center;
              margin-bottom: 20px;
              border-bottom: 2px solid #000;
              padding-bottom: 10px;
            }
            .receipt-date {
              float: left;
              font-size: 11px;
            }
            .receipt-title {
              float: right;
              font-size: 11px;
              font-weight: bold;
            }
            .clear {
              clear: both;
            }
            .order-details-title {
              text-align: center;
              font-size: 16px;
              font-weight: bold;
              margin: 20px 0;
            }
            .items-table {
              width: 100%;
              border-collapse: collapse;
              margin-bottom: 20px;
            }
            .items-table th,
            .items-table td {
              border: 1px solid #000;
              padding: 8px;
              text-align: left;
            }
            .items-table th {
              background-color: #f0f0f0;
              font-weight: bold;
              text-align: center;
            }
            .items-table td:nth-child(2),
            .items-table td:nth-child(3),
            .items-table td:nth-child(4) {
              text-align: center;
            }
            .summary-section {
              margin-top: 20px;
            }
            .summary-row {
              display: flex;
              justify-content: space-between;
              margin-bottom: 5px;
            }
            .summary-row.total {
              font-weight: bold;
              border-top: 1px solid #000;
              padding-top: 5px;
              margin-top: 10px;
            }
            .payment-method {
              margin-top: 20px;
              font-weight: bold;
            }
            @media print {
              body {
                padding: 0;
              }
              .no-print {
                display: none;
              }
            }
          </style>
        </head>
        <body>
          <div class="receipt-header">
            <div class="receipt-date">${new Date().toLocaleDateString()} ${new Date().toLocaleTimeString()}</div>
            <div class="receipt-title">Order Receipt</div>
            <div class="clear"></div>
          </div>

          <div class="order-details-title">Order Details</div>

          <table class="items-table">
            <thead>
              <tr>
                <th>Item Name</th>
                <th>Qty</th>
                <th>Price</th>
                <th>Subtotal</th>
              </tr>
            </thead>
            <tbody>
              ${orderItems.map(item => `
                <tr>
                  <td>${item.name}</td>
                  <td>${item.qty}</td>
                  <td>$${item.price.toFixed(2)}</td>
                  <td>$${item.subtotal.toFixed(2)}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>

          <div class="summary-section">
            <div class="summary-row">
              <span>Sub Total:</span>
              <span>$${subTotal.toFixed(2)}</span>
            </div>
            <div class="summary-row">
              <span>Surcharge:</span>
              <span>$${surcharge.toFixed(2)}</span>
            </div>
            <div class="summary-row">
              <span>Order Discount:</span>
              <span>$${orderDiscount.toFixed(2)}</span>
            </div>
            <div class="summary-row">
              <span>Tax:</span>
              <span>$${tax.toFixed(2)}</span>
            </div>
            <div class="summary-row total">
              <span>Bill Amount:</span>
              <span>$${billAmount.toFixed(2)}</span>
            </div>
          </div>

          <div class="payment-method">
            Payment Method: ${paymentMethod}
          </div>
        </body>
      </html>
    `;

    printWindow.document.write(printContent);
    printWindow.document.close();
    printWindow.focus();
    printWindow.print();
    printWindow.close();
  };

  // Extract order items from API data
  const orderItems: OrderItem[] = data?.product?.map((item: any) => ({
    name: item.name,
    qty: data.productWithQty.find((p: any) => p.productId === item._id)?.qty || 1,
    price: item.price,
    subtotal: item.price * (data.productWithQty.find((p: any) => p.productId === item._id)?.qty || 1)
  })) || [];

  // Extract order summary data from API
  const subTotal = data?.lineValue || 0;
  const surcharge = data?.surCharge || 0;
  const orderDiscount = 0; // Assuming no discount in the data structure
  const tax = data?.lineValueTax || 0;
  const billAmount = data?.grandTotal || 0;
  const paymentMethod = "Cash"; // Assuming cash payment
  const notes = "Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book."; // No notes field in the provided data structure

  // Footer with action buttons
  const footer = (
    <div className="flex justify-between items-center">
      <div></div> {/* Empty div for spacing */}
      <div className="flex space-x-4">
        <button
          onClick={onClose}
          className="px-10 py-2.5 border border-gray-300 text-gray-700 rounded-full hover:bg-gray-100 transition-colors"
        >
          Cancel
        </button>
        <button
          onClick={handlePrint}
          className="px-10 py-2.5 bg-black cursor-pointer text-white rounded-full hover:bg-gray-800 transition-colors flex items-center"
        >
          <Printer className="w-4 h-4 mr-2" />
          Reprint
        </button>
      </div>
    </div>
  );

  return (
    <CustomModal
      isOpen={isOpen}
      onClose={onClose}
      title="Order Details"
      width="max-w-xl"
      footer={footer}
      zIndex={100}
    >
      {isLoading ? (
        <div className="flex justify-center py-8 items-center">
          <div className="h-10 w-10 animate-spin rounded-full border-4 border-transparent border-t-orange-500 border-r-orange-500"></div>
          <div className="ml-3 text-orange-500">
            Loading
          </div>
        </div>
      ) : error ? (
        <div className="text-center py-10 text-red-500">
          <p>Error loading order data. Please try again.</p>
        </div>
      ) : !data ? (
        <div className="text-center py-10">
          <p>No order data available.</p>
        </div>
      ) : (
        <div ref={contentRef} className="px-6 py-4">
          {/* Items Table */}
          <div className="mb-6">
            <table className="w-full border-separate border-spacing-y-4">
              <thead>
                <tr className="text-left text-gray-500 border-b border-gray-100">
                  <th className="pb-2 font-medium text-sm">ITEM NAME</th>
                  <th className="pb-2 font-medium text-sm text-center">QTY</th>
                  <th className="pb-2 font-medium text-sm text-right">PRICE</th>
                  <th className="pb-2 font-medium text-sm text-right">SUBTOTAL</th>
                </tr>
              </thead>
              <tbody>
                {orderItems.length > 0 ? (
                  orderItems.map((item, index) => (
                    <tr key={index} className="border-b border-gray-100">
                      <td className="py-3 font-medium">{item.name}</td>
                      <td className="py-3 text-center">{item.qty}</td>
                      <td className="py-3 text-right">${item.price.toFixed(2)}</td>
                      <td className="py-3 text-right">${item.subtotal.toFixed(2)}</td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={4} className="py-3 text-center text-gray-500">No items found</td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>

          {/* Notes and Order Summary - Side by Side */}
          <div className="flex mb-6 gap-6">
            {/* Notes Section - Left Side */}
            <div className="w-1/2">
              <div className="bg-gray-50 rounded-lg p-4 h-full">
                <h3 className="font-medium mb-2 text-gray-700">Notes</h3>
                <p className="text-gray-600 text-sm">{notes}</p>
              </div>
            </div>

            {/* Order Summary - Right Side */}
            <div className="w-1/2 space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Sub Total</span>
                <span className="font-medium">${subTotal.toFixed(2)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Surcharge</span>
                <span className="font-medium">${surcharge.toFixed(2)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Order Discount</span>
                <span className="font-medium">${orderDiscount.toFixed(2)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Tax</span>
                <span className="font-medium">${tax.toFixed(2)}</span>
              </div>
              <div className="flex justify-between items-center pt-2 border-t border-gray-200">
                <span className="font-semibold">Bill Amount</span>
                <span className="font-semibold text-orange-500">${billAmount.toFixed(2)}</span>
              </div>
            </div>
          </div>

          {/* Payment Method */}
          <div className="mt-4 pt-3 border-t border-gray-200">
            <div className="flex items-center">
              <span className="text-gray-600 mr-2">Payment Method</span>
              <div className="flex items-center bg-gray-100 px-3 py-1 rounded-full">
                <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center mr-2">
                  <span className="text-white text-xs">$</span>
                </div>
                <span className="font-medium">{paymentMethod}</span>
              </div>
            </div>
          </div>
        </div>
      )}
    </CustomModal>
  );
};

export default PrintLastReceipt;
