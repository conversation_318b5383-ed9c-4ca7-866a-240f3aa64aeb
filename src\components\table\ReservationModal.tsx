import React, { useState, useMemo } from 'react';
import { IoClose } from "react-icons/io5";
import { IoIosArrowBack, IoIosArrowForward } from "react-icons/io";
import { format, addDays, subDays, startOfWeek, parseISO, isSameDay } from 'date-fns';
import NewReservationModal from './NewReservationModal';
import { useGetTableOperatorQuery } from '../../store/api/pos/orderapi';
import { motion, AnimatePresence } from 'framer-motion';

interface ReservationModalProps {
  isOpen: boolean;
  onClose: () => void;
}

interface TableInfo {
  _id: string;
  tableNo: string;
  tableName: string;
  capacity: number;
  Status: string;
  location: string;
  [key: string]: any;
}

interface ReservationData {
  _id: string;
  fullName: string;
  reserveDate: string;
  reserveTime: string;
  partySize: number;
  site: string;
  table: TableInfo;
  createdAt: string;
  updatedAt: string;
  userId: string;
}

const ReservationModal: React.FC<ReservationModalProps> = ({ isOpen, onClose }) => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [isNewReservationOpen, setIsNewReservationOpen] = useState(false);
  const userId = localStorage.getItem("userId") || "";
  const { data: tableOperators, isLoading, error } = useGetTableOperatorQuery(userId);

  // Filter reservations for selected date
  const filteredReservations = useMemo(() => {
    if (!tableOperators || !selectedDate) return [];

    return tableOperators.filter((reservation: ReservationData) => {
      const reservationDate = parseISO(reservation.reserveDate);
      return isSameDay(reservationDate, selectedDate);
    }).sort((a: ReservationData, b: ReservationData) => {
      // Sort by time
      return a.reserveTime.localeCompare(b.reserveTime);
    });
  }, [tableOperators, selectedDate]);

  // Handle closing new reservation
  const handleCloseNewReservation = () => {
    setIsNewReservationOpen(false);
  };

  // Generate week days
  const generateWeekDays = (startDate: Date) => {
    const days = [];
    for (let i = 0; i < 7; i++) {
      days.push(addDays(startDate, i));
    }
    return days;
  };

  const weekDays = generateWeekDays(startOfWeek(currentDate));

  const handlePreviousWeek = () => {
    setCurrentDate(subDays(currentDate, 7));
  };

  const handleNextWeek = () => {
    setCurrentDate(addDays(currentDate, 7));
  };

  const handleDateSelect = (date: Date) => {
    setSelectedDate(date);
  };

  const modalVariants = {
    hidden: { x: '100%' },   // Start off-screen right
    visible: { x: 0 },       // Slide in to position
    exit: { x: '100%' },     // Slide out to right
  };

  return (
    <>
      <AnimatePresence>
        {isOpen && (
          <div className="fixed inset-0 z-50 flex justify-end">
            <motion.div
              className="bg-black/50 absolute inset-0"
              onClick={onClose}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.3 }}
            />
            <motion.div
              className="relative w-[500px] bg-white h-full flex flex-col"
              initial="hidden"
              animate="visible"
              exit="exit"
              variants={modalVariants}
              transition={{ type: "tween", duration: 0.3 }}
            >
              {/* Header */}
              <div className="flex items-center justify-between p-4 border-b border-[#E4E4E4]">
                <h2 className="text-xl font-semibold">Reservation</h2>
                <button onClick={onClose} className='cursor-pointer' >
                  <IoClose size={24} />
                </button>
              </div>

              <div className="flex-1 overflow-y-auto">
                {/* Date Selection */}
                <div className="p-4">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-2">
                      <button
                        className="p-2 hover:bg-[#FF5C00] cursor-pointer hover:text-white rounded-lg text-black"
                        onClick={handlePreviousWeek}
                      >
                        <IoIosArrowBack />
                      </button>
                      <span className="text-gray-600">Select date</span>
                    </div>
                    <span className='font-bold ml-25'>{format(currentDate, 'MMMM yyyy')}</span>
                    <button
                      className="p-2 hover:bg-[#FF5C00] hover:text-white rounded-lg text-black cursor-pointer "
                      onClick={handleNextWeek}
                    >
                      <IoIosArrowForward />
                    </button>
                  </div>

                  {/* Calendar Days */}
                  <div className="rounded-2xl border border-[#E4E4E4] overflow-hidden">
                    <div className="grid grid-cols-7 text-center border-x-2 border-[#E4E4E4]">
                      {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
                        <div key={day} className="text-gray-500 text-xs">
                          {day}
                        </div>
                      ))}

                      {weekDays.map((date, index) => (
                        <div
                          key={index}
                          onClick={() => handleDateSelect(date)}
                          className={`py-2 cursor-pointer ${selectedDate && format(selectedDate, 'yyyy-MM-dd') === format(date, 'yyyy-MM-dd')
                            ? 'bg-orange-100 text-orange-500 font-bold'
                            : 'hover:bg-gray-50 font-bold'
                            }`}
                        >
                          {format(date, 'd')}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Reservation List */}
                <div>
                  {/* Header */}
                  <div className="grid grid-cols-4 px-4 py-3 text-xs text-gray-500 border-y border-[#E4E4E4]">
                    <div>Name</div>
                    <div>Time</div>
                    <div>Table No</div>
                    <div>People</div>
                  </div>

                  {/* Loading State */}
                  {isLoading && (
                    <div className="text-center py-8 text-gray-500">
                      Loading reservations...
                    </div>
                  )}

                  {/* Error State */}
                  {error && (
                    <div className="text-center py-8 text-red-500">
                      Error loading reservations
                    </div>
                  )}

                  {/* Empty State */}
                  {!isLoading && !error && filteredReservations.length === 0 && (
                    <div className="text-center py-8 text-gray-500">
                      No reservations for {format(selectedDate, 'MMM d, yyyy')}
                    </div>
                  )}

                  {/* List Items */}
                  {!isLoading && !error && filteredReservations.map((reservation: ReservationData) => (
                    <div
                      key={reservation._id}
                      className="grid grid-cols-4 px-4 py-4 text-sm border-b border-[#E4E4E4] hover:bg-gray-50"
                    >
                      <div className="font-medium">{reservation.fullName || 'Guest'}</div>
                      <div className="text-black">{reservation.reserveTime}</div>
                      <div className="text-black">
                        {reservation.table ? `T-${reservation.table.tableNo}` : 'N/A'}
                      </div>
                      <div className="flex items-center gap-1">
                        <svg
                          className="w-4 h-4"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                        >
                          <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" />
                          <circle cx="12" cy="7" r="4" />
                        </svg>
                        {reservation.partySize || 0}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Footer */}
              <div className="p-4 mt-auto">
                <button
                  className="w-full bg-[#FF5C00] text-white py-3 rounded-full font-medium hover:bg-orange-600"
                  onClick={() => setIsNewReservationOpen(true)}
                >
                  Add New Reservation
                </button>
              </div>
            </motion.div>
          </div>
        )}
      </AnimatePresence>

      {/* Add the New Reservation Modal */}
      <NewReservationModal
        isOpen={isNewReservationOpen}
        onClose={handleCloseNewReservation}
        onContinue={() => { }}
      />
    </>
  );
};

export default ReservationModal;