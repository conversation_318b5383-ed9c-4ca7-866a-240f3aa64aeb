import { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useGetProductsQuery } from "../store/api/menuitemApi";
import { useGetCategoriesQuery } from "../store/api/CategoryApi";
import { addItem, selectCartItems } from "../store/slices/cartSlice";
import Navbar from "../components/Navbar";
import Sidebar from "../components/Sidebar";
import SearchBar from "../components/SearchBar";
import Categories from "../components/Categories";
import ProductCard from "../components/ProductCard";
import InvoicePanel from "../components/InvoicePanel";
import OutOfStockAlert from "../components/OutOfStockAlert";
import PurchaseHistory from "../components/posModals/reportModals/PurchaseHistory";
import AddCustomer from "../components/posModals/AddCustomer";
import BillDemonstration from "../components/posModals/reportModals/BillDemonstration";
import defaultProductImage from "../assets/Home/product.jpg";
import CloseOutReports from "../components/posModals/reportModals/CloseOutReports";
import InventorySummaryReport from "../components/posModals/reportModals/InventorySummaryReport";
import InventorySalesReport from "../components/posModals/reportModals/InventorySalesReport";
import ModifierSalesReport from "../components/posModals/reportModals/ModifierSalesReport";
import XReportSalesSummary from "../components/posModals/reportModals/XReportSalesSummary";
import ZReportSalesSummary from "../components/posModals/reportModals/ZReportSalesSummary";
import NewMembers from "../components/posModals/reportModals/NewMembers";
import EmployeeAccessRecord from "../components/posModals/reportModals/EmployeeAccessRecord";
import CustomerBalance from "../components/posModals/reportModals/CustomerBalance";
import AccountPaymentReport from "../components/posModals/reportModals/AccountPaymentReport";
import AdditionalItemModal from "../components/posModals/reportModals/AdditionalItemModal";
import {
  RefundOrders as RefundableOrders,
  OrdersHistory,
  Tips,
  LoadReceipts,
  PointsAdjustment,
  BalanceAdjustment,
  ReceivedPayments
} from "../components/posModals/settingsModals";
import PrintLastReceipt from "../components/posModals/reportModals/PrintLastReceipt";
import { useGetModifiersQuery } from "../store/api/pos/orderapi";
import type { ModifierOption } from "../types/modifiers";

const Home = () => {
  const dispatch = useDispatch();
  const cartItems = useSelector(selectCartItems);

  console.log("cartitems", cartItems);

  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("All");
  const [activeModal, setActiveModal] = useState<string | null>(null);
  const [selectedParentCategory, setSelectedParentCategory] = useState<string | null>(null);
  const [isInvoiceModalOpen, setIsInvoiceModalOpen] = useState(false);

  // State for product modifier modal
  const [isModifierModalOpen, setIsModifierModalOpen] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<any>(null);
  const [productModifiers, setProductModifiers] = useState<any[]>([]);

  // State for out of stock alert
  const [isOutOfStockAlertOpen, setIsOutOfStockAlertOpen] = useState(false);
  const [outOfStockProductName, setOutOfStockProductName] = useState("");

  const userId = localStorage.getItem('userId') || '';

  // Fetch products and categories
  const {
    data: products = [],
    isLoading: productsLoading,
    error: productsError
  } = useGetProductsQuery(userId);

  const {
    data: categories = [],
  } = useGetCategoriesQuery(userId);

  const { data: Modifier } = useGetModifiersQuery(userId)
  console.log("the modifier is ", Modifier)

  // Filter categories based on selected parent category
  const getFilteredCategories = () => {
    // If no parent category is selected or "All" is selected, show all categories
    if (!selectedParentCategory || selectedParentCategory === "All") {
      return ["All", ...categories.filter(cat => cat.status === true).map(cat => cat.name)];
    }

    // Otherwise, filter categories by the selected parent category
    const filteredCategories = categories.filter(
      cat => cat.parentCategory === selectedParentCategory && cat.status === true
    );

    console.log("Filtered subcategories for parent", selectedParentCategory, ":", filteredCategories);

    // Always include "All" option
    return ["All", ...filteredCategories.map(cat => cat.name)];
  };

  // Get filtered categories for the Categories component
  const categoryOptions = getFilteredCategories();

  // Helper function to check if a product belongs to a category
  const productBelongsToCategory = (product: any, categoryName: string) => {
    // Handle case where categoryId is an array of objects
    if (Array.isArray(product.categoryId) && product.categoryId.length > 0) {
      return product.categoryId.some((cat: any) => cat.name === categoryName);
    }
    return false;
  };

  // Log for debugging
  useEffect(() => {
    console.log("Selected parent category:", selectedParentCategory);
    console.log("Selected category:", selectedCategory);
    console.log("Filtered category options:", categoryOptions);

    // Log which products belong to the selected category
    if (selectedCategory !== "All") {
      const matchingProducts = products.filter(p => productBelongsToCategory(p, selectedCategory));
      console.log("Products matching selected category:", matchingProducts.map(p => p.name));
    }
  }, [selectedParentCategory, selectedCategory, categoryOptions, products]);

  // List of all report and settings modal names
  const reportAndSettingsModals = [
    "Purchase History", "Bill Demonstration", "Cashier", "Inventory Reports",
    "inventory Sales Reports", "Modifier Reports", "X Report-Sales Summary",
    "Z Report-Sales Summary", "New Member", "Employee Access Record",
    "Customer Balance", "Account Payment Report", "Refundable Orders",
    "Time Clock", "Clock Register", "History", "Tips", "Load Recipts",
    "Point Adjustment", "Pay Account", "Receive Payment", "Value Refund Reason",
    "Print Last Receipt", "Add Customer"
  ];

  // Store the previous parent category and selected category before opening a modal
  const [prevParentCategory, setPrevParentCategory] = useState<string | null>(null);
  const [prevSelectedCategory, setPrevSelectedCategory] = useState<string>("All");

  const handleModalOpen = (modalName: string) => {
    // Check if this is a report or settings modal
    if (reportAndSettingsModals.includes(modalName)) {
      // Save current categories before opening the modal
      setPrevParentCategory(selectedParentCategory);
      setPrevSelectedCategory(selectedCategory);

      // Just set the active modal without changing the categories
      setActiveModal(modalName);
    } else {
      // This is a regular category selection from the sidebar
      setSelectedParentCategory(modalName);
      // Reset the selected category to "All" when a new parent category is selected
      setSelectedCategory("All");
      setActiveModal(modalName);
    }
  };

  const handleModalClose = () => {
    // Check if we're closing a report or settings modal
    if (activeModal && reportAndSettingsModals.includes(activeModal)) {
      // Restore the previous categories
      if (prevParentCategory !== null) {
        setSelectedParentCategory(prevParentCategory);
      }
      setSelectedCategory(prevSelectedCategory);
    }

    setActiveModal(null);
  };

  // Helper function to generate the category message for no products
  const getCategoryMessage = () => {
    if (selectedParentCategory && selectedParentCategory !== "All") {
      if (selectedCategory !== "All") {
        // Both parent and subcategory are selected
        return `"${selectedParentCategory} > ${selectedCategory}"`;
      } else {
        // Only parent category is selected
        return `"${selectedParentCategory}"`;
      }
    } else if (selectedCategory !== "All") {
      // Only subcategory is selected (no parent)
      return `"${selectedCategory}"`;
    } else {
      // All categories
      return "this selection";
    }
  };

  const filteredProducts = products.filter((p) => {
    // Always include search term filter
    const matchesSearch = p.name.toLowerCase().includes(searchTerm.toLowerCase());
    if (!matchesSearch) return false;

    // If no parent category is selected and "All" category is selected, show all products
    if (selectedCategory === "All" && !selectedParentCategory) {
      return true;
    }

    // Filter by parent category from sidebar
    if (selectedParentCategory && selectedParentCategory !== "All") {
      // Find subcategories that belong to this parent
      const subcategoriesForParent = categories.filter(
        cat => cat.parentCategory === selectedParentCategory && cat.status === true
      );

      // If a specific subcategory is selected
      if (selectedCategory !== "All") {
        // Check if product belongs to the selected subcategory
        return productBelongsToCategory(p, selectedCategory);
      }

      // Otherwise, show all products that belong to any subcategory of this parent
      return subcategoriesForParent.some(subcat =>
        productBelongsToCategory(p, subcat.name)
      );
    }

    // Filter by category from Categories component
    if (selectedCategory !== "All") {
      return productBelongsToCategory(p, selectedCategory);
    }

    // Default case
    return true;
  });

  const handleAddToCart = (product: any) => {
    console.log("product", product);

    // Check if product is out of stock
    if (product.totalQuantity <= 0) {
      setOutOfStockProductName(product.name);
      setIsOutOfStockAlertOpen(true);
      return;
    }

    // Check if the product has modifiers
    const mods = Modifier?.filter(
      (mod: any) => mod.productId?._id === (product.id || product._id) && mod.isActive
    );

    const hasModifiers = mods && mods.length > 0;

    if (hasModifiers) {
      // If product has modifiers, show the modifier selection modal
      setSelectedProduct({
        id: product.id || product._id,
        name: product.name,
        price: product.price,
        image: product.pictureUrl || product.image,
        totalQuantity: product.totalQuantity
      });
      setProductModifiers(mods);
      setIsModifierModalOpen(true);
    } else {
      // If no modifiers, add directly to cart
      dispatch(addItem({
        id: product.id || product._id,
        name: product.name,
        price: product.price,
        quantity: 1,
        image: product.pictureUrl || product.image,
        hasModifiers: false
      }));
    }
  };

  useEffect(() => {
    const handleResize = () => {
      setSidebarOpen(window.innerWidth >= 1024); // Use lg breakpoint (1024px) for better tablet and laptop support
    };

    handleResize();
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  return (
    <div className="min-h-screen flex flex-col bg-white overflow-hidden ">
      <Navbar toggleSidebar={() => setSidebarOpen((prev) => !prev)} />

      <div className="flex flex-1 pt-16 overflow-x-hidden">
        {" "}
        <Sidebar
          isOpen={sidebarOpen}
          onModalOpen={handleModalOpen}
          onCategoriesLoaded={(parentCategories) => {
            // This function will be called when parent categories are loaded in the sidebar
            console.log("Parent categories loaded:", parentCategories);
            // You can perform additional actions with the parent categories if needed
          }}
        />
        {/* Main Content Area */}
        <div
          className={`flex-1 flex flex-col lg:flex-row xl:flex-row ${sidebarOpen && window.innerWidth >= 1024 ? "ml-[280px]" : ""
            } transition-all duration-300 overflow-hidden`}
        >
          {/* Left Section - Products */}
          <div className="flex-1 flex flex-col h-[calc(100vh-64px)] lg:max-w-[calc(100%-380px)] xl:max-w-[calc(100%-400px)] overflow-hidden">
            {/* Search and Categories Section */}
            <div className={`sticky top-0 bg-white border-b border-b-[#E4E4E4] ${activeModal || isModifierModalOpen || isOutOfStockAlertOpen ? 'z-10' : 'z-10'
              }`}>
              <div className="flex flex-col lg:flex-row xl:flex-row">
                <div className="w-full lg:w-[270px] xl:w-[270px] lg:border-r xl:border-r lg:border-[#E4E4E4] xl:border-[#E4E4E4] pt-2 flex-shrink-0">
                  <SearchBar
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
                <div className="w-full lg:w-[calc(100%-270px)] lg:max-w-[400px] xl:w-[calc(100%-270px)] xl:max-w-[500px] min-w-0 overflow-hidden flex-shrink-0">
                  <Categories
                    categories={categoryOptions}
                    selected={selectedCategory}
                    onSelect={setSelectedCategory}
                  />
                </div>
              </div>
            </div>

            {/* Products Grid */}
            <div className="flex-1 overflow-y-auto scrollbar-hide bg-white">
              {productsLoading ? (
                <div className="text-center py-4">Loading products...</div>
              ) : productsError ? (
                <div className="text-center py-4 text-red-500">
                  Error loading products
                </div>
              ) : filteredProducts.length === 0 ? (
                <div className="flex flex-col items-center justify-center py-16 px-4">
                  {/* Illustration */}
                  <div className="mb-8">
                    <svg width="200" height="200" viewBox="0 0 400 400" className="text-orange">
                      {/* Chef character */}
                      <g>
                        {/* Chef body */}
                        <ellipse cx="200" cy="320" rx="60" ry="40" fill="#f3f4f6" />
                        <rect x="160" y="240" width="80" height="80" rx="10" fill="white" stroke="#e5e7eb" strokeWidth="2" />

                        {/* Chef head */}
                        <circle cx="200" cy="180" r="40" fill="#fef3c7" />

                        {/* Chef hat */}
                        <ellipse cx="200" cy="140" rx="45" ry="25" fill="white" stroke="#e5e7eb" strokeWidth="2" />
                        <rect x="180" y="130" width="40" height="20" fill="white" />

                        {/* Chef face */}
                        <circle cx="190" cy="175" r="2" fill="#374151" />
                        <circle cx="210" cy="175" r="2" fill="#374151" />
                        <path d="M 190 185 Q 200 195 210 185" stroke="#374151" strokeWidth="2" fill="none" />

                        {/* Arms */}
                        <ellipse cx="140" cy="260" rx="15" ry="30" fill="#fef3c7" />
                        <ellipse cx="260" cy="260" rx="15" ry="30" fill="#fef3c7" />

                        {/* Cooking pot */}
                        <ellipse cx="200" cy="280" rx="25" ry="15" fill="#374151" />
                        <rect x="175" y="265" width="50" height="20" fill="#374151" />
                        <ellipse cx="200" cy="265" rx="25" ry="8" fill="#1f2937" />

                        {/* Steam/question marks */}
                        <g className="animate-pulse">
                          <text x="220" y="240" fontSize="20" fill="#FF5C00">?</text>
                          <text x="240" y="220" fontSize="16" fill="#FF5C00">?</text>
                          <text x="180" y="230" fontSize="18" fill="#FF5C00">?</text>
                        </g>

                        {/* Chef apron */}
                        <path d="M 170 250 L 230 250 L 225 300 L 175 300 Z" fill="#FF5C00" opacity="0.8" />
                      </g>
                    </svg>
                  </div>

                  {/* No Products Message */}
                  <h2 className="text-2xl font-bold text-gray-800 mb-4">
                    No Products Available
                  </h2>
                  <p className="text-gray-600 text-center max-w-md leading-relaxed">
                    There are currently no products in the {getCategoryMessage()} category. Please check back later or explore other categories.
                  </p>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4 xl:p-10 p-3">
                  {filteredProducts.map((product) => (
                    <ProductCard
                      key={product.id}
                      name={product.name}
                      price={product.price}
                      image={product.pictureUrl || defaultProductImage}
                      onClick={() => handleAddToCart(product)}
                    />
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Right Section - Invoice Panel */}
          <div className="hidden lg:block lg:w-[380px] xl:w-[400px] sticky top-0 h-[calc(100vh-64px)] border-l border-gray-200 z-10 flex-shrink-0">
            <InvoicePanel />
          </div>
        </div>
      </div>

      {/* Mobile Invoice Button - Floating */}
      {!isInvoiceModalOpen && (
        <div className="lg:hidden fixed bottom-6 right-6 z-40">
          <button
            onClick={() => setIsInvoiceModalOpen(true)}
            className={`relative bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white rounded-full p-4 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 group ${cartItems.length > 0 ? 'animate-float-pulse' : ''}`}
          >
            <div className="flex items-center justify-center">
              <svg
                className="w-6 h-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
            </div>
            {cartItems.length > 0 && (
              <div className="absolute -top-2 -right-2 bg-red-600 text-white text-xs rounded-full h-6 w-6 flex items-center justify-center font-bold animate-pulse">
                {cartItems.length}
              </div>
            )}
          </button>
        </div>
      )}

      {/* Mobile Invoice Modal */}
      {isInvoiceModalOpen && (
        <div className="lg:hidden fixed inset-0 z-50 flex items-end">
          {/* Backdrop */}
          <div
            className="absolute inset-0 bg-black/50 backdrop-blur-sm"
            onClick={() => setIsInvoiceModalOpen(false)}
          />

          {/* Modal Content */}
          <div className="relative w-full bg-white rounded-t-3xl shadow-2xl transform transition-all duration-300 ease-out animate-slide-up max-h-[85vh] flex flex-col">
            {/* Modal Header */}
            <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gradient-to-r from-orange-50 to-red-50 rounded-t-3xl">
              <h2 className="text-xl font-bold text-gray-800 flex items-center gap-2">
                <svg className="w-6 h-6 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                Invoice
              </h2>
              <button
                onClick={() => setIsInvoiceModalOpen(false)}
                className="p-2 hover:bg-gray-100 rounded-full transition-colors duration-200"
              >
                <svg className="w-6 h-6 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {/* Modal Body */}
            <div className="flex-1 overflow-hidden">
              <InvoicePanel isInModal={true} />
            </div>
          </div>
        </div>
      )}

      {/* Reports Modals - Rendered in a container that overlays the content */}
      <div className="modal-container" style={{ position: 'relative', zIndex: 50 }}>
        {activeModal === "Add Customer" && (
          <AddCustomer
            isOpen={true}
            onClose={handleModalClose}
            customerData={null}
            isEditing={false}
          />
        )}
        {activeModal === "Purchase History" && (
          <PurchaseHistory isOpen={true} onClose={handleModalClose} />
        )}
        {activeModal === "Bill Demonstration" && (
          <BillDemonstration isOpen={true} onClose={handleModalClose} />
        )}
        {activeModal === "Cashier" && (
          <CloseOutReports isOpen={true} onClose={handleModalClose} />
        )}
        {activeModal === "Inventory Reports" && (
          <InventorySummaryReport isOpen={true} onClose={handleModalClose} />
        )}
        {activeModal === "inventory Sales Reports" && (
          <InventorySalesReport isOpen={true} onClose={handleModalClose} />
        )}
        {activeModal === "Modifier Reports" && (
          <ModifierSalesReport isOpen={true} onClose={handleModalClose} />
        )}
        {activeModal === "X Report-Sales Summary" && (
          <XReportSalesSummary isOpen={true} onClose={handleModalClose} />
        )}
        {activeModal === "Z Report-Sales Summary" && (
          <ZReportSalesSummary isOpen={true} onClose={handleModalClose} />
        )}
        {activeModal === "New Member" && (
          <NewMembers isOpen={true} onClose={handleModalClose} />
        )}
        {activeModal === "Employee Access Record" && (
          <EmployeeAccessRecord isOpen={true} onClose={handleModalClose} />
        )}
        {activeModal === "Customer Balance" && (
          <CustomerBalance isOpen={true} onClose={handleModalClose} />
        )}
        {activeModal === "Account Payment Report" && (
          <AccountPaymentReport isOpen={true} onClose={handleModalClose} />
        )}

        {/* Settings Modals */}

        {activeModal === "Refundable Orders" && (
          <RefundableOrders isOpen={true} onClose={handleModalClose} />
        )}
        {activeModal == "Time Clock" && (
          <BillDemonstration isOpen={true} onClose={handleModalClose} />
        )}
        {activeModal == "Clock Register" && (
          <BillDemonstration isOpen={true} onClose={handleModalClose} />
        )}
        {activeModal == "History" && (
          <OrdersHistory isOpen={true} onClose={handleModalClose} />
        )}
        {activeModal == "Tips" && (
          <Tips isOpen={true} onClose={handleModalClose} />
        )}
        {activeModal == "Load Recipts" && (
          <LoadReceipts isOpen={true} onClose={handleModalClose} />
        )}
        {activeModal == "Point Adjustment" && (
          <PointsAdjustment isOpen={true} onClose={handleModalClose} />
        )}
        {activeModal == "Pay Account" && (
          <BalanceAdjustment isOpen={true} onClose={handleModalClose} />
        )}
        {activeModal == "Receive Payment" && (
          <ReceivedPayments isOpen={true} onClose={handleModalClose} />
        )}
        {activeModal == "Value Refund Reason" && (
          <BillDemonstration isOpen={true} onClose={handleModalClose} />
        )}
        {activeModal == "Print Last Receipt" && (
          <PrintLastReceipt isOpen={true} onClose={handleModalClose} />
        )}
        {activeModal == "printlastreceipt" && (
          <PrintLastReceipt isOpen={true} onClose={handleModalClose} />
        )}

      </div>

      {/* Mobile Overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 bg-black/50 bg-opacity-50 z-25 lg:hidden xl:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Product Modifier Modal */}
      {selectedProduct && (
        <AdditionalItemModal
          isOpen={isModifierModalOpen}
          onClose={() => setIsModifierModalOpen(false)}
          itemName={selectedProduct.name}
          itemPrice={selectedProduct.price}
          modifiers={productModifiers[0]?.Modifier || []}
          hasModifiers={!!(productModifiers[0]?.Modifier && productModifiers[0]?.Modifier.length > 0)}
          initialNote=""
          initialDiscountType=""
          initialDiscountAmount=""
          onApply={(discountType, discountAmount, note, selectedModifiers, totalPrice) => {
            console.log(discountType, discountAmount)

            // Check if product is out of stock before adding to cart
            if (selectedProduct.totalQuantity === 0 || selectedProduct.totalQuantity === "0") {
              setOutOfStockProductName(selectedProduct.name);
              setIsOutOfStockAlertOpen(true);
              return;
            }

            let finalPrice: number = totalPrice || selectedProduct.price;

            // Add the price of all selected modifier properties if totalPrice wasn't provided
            if (totalPrice === undefined && selectedModifiers) {
              selectedModifiers.forEach(modifier => {
                if (modifier.selectedProperty) {
                  finalPrice += Number(modifier.selectedProperty.price);
                }
              });
            }

            dispatch(addItem({
              id: selectedProduct.id,
              name: selectedProduct.name,
              price: finalPrice,
              quantity: 1,
              image: selectedProduct.image,
              modifiers: selectedModifiers as ModifierOption[],
              selectedModifiers: selectedModifiers as ModifierOption[],
              hasModifiers: !!(productModifiers[0]?.Modifier && productModifiers[0]?.Modifier.length > 0),
              note: note,
              originalPrice: selectedProduct.price,
              discountType: discountType || undefined,
              discountAmount: discountAmount || undefined
            }));
          }}
        />
      )}

      {/* Out of Stock Alert */}
      <OutOfStockAlert
        isOpen={isOutOfStockAlertOpen}
        productName={outOfStockProductName}
        onClose={() => setIsOutOfStockAlertOpen(false)}
      />
    </div>
  );
};

export default Home;